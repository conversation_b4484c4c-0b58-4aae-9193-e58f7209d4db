{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "1.0.0.0", "parameters": {"containerAppEnvName": {"type": "string"}, "containerAppName": {"type": "string"}, "location": {"type": "string"}, "containerAppRg": {"type": "string"}, "acrLoginServer": {"type": "string"}, "acrUsername": {"type": "string"}, "acrPassword": {"type": "securestring"}, "imageName": {"type": "string"}, "targetPort": {"type": "int", "defaultValue": 80}, "useManagedIdentity": {"type": "bool", "defaultValue": false}}, "resources": [{"type": "Microsoft.App/managedEnvironments", "apiVersion": "2022-10-01", "name": "[parameters('containerAppEnvName')]", "location": "[parameters('location')]", "properties": {"workloadProfiles": [{"name": "Consumption", "workloadProfileType": "Consumption"}]}}, {"type": "Microsoft.App/containerApps", "apiVersion": "2022-10-01", "name": "[parameters('containerAppName')]", "location": "[parameters('location')]", "identity": "[if(parameters('useManagedIdentity'), createObject('type', 'SystemAssigned'), null())]", "dependsOn": ["[resourceId('Microsoft.App/managedEnvironments', parameters('containerAppEnvName'))]"], "properties": {"managedEnvironmentId": "[resourceId('Microsoft.App/managedEnvironments', parameters('containerAppEnvName'))]", "configuration": {"ingress": {"external": true, "targetPort": "[parameters('targetPort')]"}, "registries": "[if(parameters('useManagedIdentity'), createArray(createObject('server', parameters('acrLoginServer'), 'identity', 'system')), createArray(createObject('server', parameters('acrLoginServer'), 'username', parameters('acrUsername'), 'passwordSecretRef', 'acr-pw')))]", "secrets": "[if(parameters('useManagedIdentity'), createArray(), createArray(createObject('name', 'acr-pw', 'value', parameters('acrPassword'))))]"}, "template": {"containers": [{"name": "[parameters('containerAppName')]", "image": "[format('{0}/{1}', parameters('acrLoginServer'), parameters('imageName'))]", "resources": {"cpu": 0.25, "memory": "0.5Gi"}}]}, "workloadProfileName": "Consumption"}}]}