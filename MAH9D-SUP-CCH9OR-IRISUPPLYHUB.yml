trigger:
  branches:
    include:
    - main
variables:
- name: appName
  value: 'mah9-cch9-irisupplyhub-d02'
- name: buildConfiguration
  value: 'Release'
stages:
- stage: __default
  jobs:
  - job: Job
    pool:
      vmImage: 'windows-latest'
    steps:
    - task: AzureAppServiceManage@0
      displayName: 'Stop Azure App Service'
      inputs:
        azureSubscription: 'MAH9-SUP-CCH9 (41ae4571-862f-4682-ac9d-c8e39c9ce301)'
        action: 'Stop Azure App Service'
        webAppName: '$(appName)'
      continueOnError: true
    - task: PowerShell@2
      displayName: 'Clean build directory'
      inputs:
        targetType: 'inline'
        script: |
          # Remove common files that shouldn't be deployed
          if (Test-Path "$(Build.SourcesDirectory)/.git") { Remove-Item -Recurse -Force "$(Build.SourcesDirectory)/.git" }
          if (Test-Path "$(Build.SourcesDirectory)/.gitignore") { Remove-Item -Force "$(Build.SourcesDirectory)/.gitignore" }
          if (Test-Path "$(Build.SourcesDirectory)/azure-pipelines.yml") { Remove-Item -Force "$(Build.SourcesDirectory)/azure-pipelines.yml" }
          if (Test-Path "$(Build.SourcesDirectory)/README.md") { Remove-Item -Force "$(Build.SourcesDirectory)/README.md" }

          # List remaining files for debugging
          Write-Host "Files to be deployed:"
          Get-ChildItem -Recurse "$(Build.SourcesDirectory)" | Select-Object FullName
    - task: ArchiveFiles@2
      displayName: 'Create deployment package'
      inputs:
        rootFolderOrFile: '$(Build.SourcesDirectory)'
        includeRootFolder: false
        archiveType: 'zip'
        archiveFile: '$(Build.ArtifactStagingDirectory)/$(appName).zip'
        replaceExistingArchive: true
        verbose: true
    - task: AzureWebApp@1
      displayName: 'Deploy to Azure App Service'
      inputs:
        azureSubscription: 'MAH9-SUP-CCH9 (41ae4571-862f-4682-ac9d-c8e39c9ce301)'
        appType: 'webApp'
        appName: '$(appName)'
        package: '$(Build.ArtifactStagingDirectory)/$(appName).zip'
        deploymentMethod: 'zipDeploy'
        takeAppOfflineFlag: true
        removeAdditionalFilesFlag: true
    - task: AzureAppServiceManage@0
      displayName: 'Start Azure App Service'
      inputs:
        azureSubscription: 'MAH9-SUP-CCH9 (41ae4571-862f-4682-ac9d-c8e39c9ce301)'
        action: 'Start Azure App Service'
        webAppName: '$(appName)'

