# Variables
$SOURCE_ACR_NAME = "mobileaspectsacr"
$SOURCE_ACR_RESOURCE_GROUP = "Containerlab"
$SOURCE_ACR_SUBSCRIPTION_ID = "********-6c21-4a42-a096-945280fd24c0" # <-- Set your ACR's subscription ID here
$IMAGE_NAME = "irishome:keycloak"
$TARGET_SUBSCRIPTION_ID = "41ae4571-862f-4682-ac9d-c8e39c9ce301"
$CONTAINER_APP_RG = "mah9-all-cch9-rgr-d01"
$CONTAINER_APP_ENV = "mah9-all-cch9-cae-d01"
$CONTAINER_APP_NAME = "irishome"
$LOCATION = "East US 2"

# Log in to Azure
az login

# Set target subscription
az account set --subscription $TARGET_SUBSCRIPTION_ID

# Get ACR credentials (if not using managed identity)
Write-Host "Running: az acr credential show -n $SOURCE_ACR_NAME --query 'username' -o tsv --subscription $SOURCE_ACR_SUBSCRIPTION_ID"
$rawUsername = az acr credential show -n $SOURCE_ACR_NAME --query "username" -o tsv --subscription $SOURCE_ACR_SUBSCRIPTION_ID
Write-Host "Output: $rawUsername"
$ACR_USERNAME = $rawUsername.Trim()

Write-Host "Running: az acr credential show -n $SOURCE_ACR_NAME --query 'passwords[0].value' -o tsv --subscription $SOURCE_ACR_SUBSCRIPTION_ID"
$rawPassword = az acr credential show -n $SOURCE_ACR_NAME --query "passwords[0].value" -o tsv --subscription $SOURCE_ACR_SUBSCRIPTION_ID
Write-Host "Output: $rawPassword"
$ACR_PASSWORD = $rawPassword.Trim()

Write-Host "Running: az acr show -n $SOURCE_ACR_NAME --query 'loginServer' -o tsv --subscription $SOURCE_ACR_SUBSCRIPTION_ID"
$rawLoginServer = az acr show -n $SOURCE_ACR_NAME --query "loginServer" -o tsv --subscription $SOURCE_ACR_SUBSCRIPTION_ID
Write-Host "Output: $rawLoginServer"
$ACR_LOGIN_SERVER = $rawLoginServer.Trim()

# Create Container App Environment (if needed)
az containerapp env create `
  --name $CONTAINER_APP_ENV `
  --resource-group $CONTAINER_APP_RG `
  --location "$LOCATION"

# Deploy Container App
Write-Host "ACR_LOGIN_SERVER: '$ACR_LOGIN_SERVER'"
Write-Host "ACR_USERNAME: '$ACR_USERNAME'"
Write-Host "ACR_PASSWORD: '$ACR_PASSWORD'"

az containerapp create `
  --name $CONTAINER_APP_NAME `
  --resource-group $CONTAINER_APP_RG `
  --environment $CONTAINER_APP_ENV `
  --image "$ACR_LOGIN_SERVER/$IMAGE_NAME" `
  --registry-server "$ACR_LOGIN_SERVER" `
  --registry-username "$ACR_USERNAME" `
  --registry-password "$ACR_PASSWORD" `
  --ingress external `
  --target-port 80
