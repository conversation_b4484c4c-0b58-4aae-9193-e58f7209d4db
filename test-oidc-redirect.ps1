# Test OIDC Redirect Configuration
# This script helps verify the redirect URI configuration

$containerAppUrl = "https://irishome.nicetree-50b00068.eastus2.azurecontainerapps.io"
$callbackPath = "/signin-oidc"
$fullRedirectUri = "$containerAppUrl$callbackPath"

Write-Host "Container App OIDC Configuration Test" -ForegroundColor Yellow
Write-Host "=====================================" -ForegroundColor Yellow

Write-Host "`nContainer App Details:" -ForegroundColor Cyan
Write-Host "App URL: $containerAppUrl" -ForegroundColor White
Write-Host "Callback Path: $callbackPath" -ForegroundColor White
Write-Host "Full Redirect URI: $fullRedirectUri" -ForegroundColor White

Write-Host "`nKeycloak Client Configuration Required:" -ForegroundColor Cyan
Write-Host "Valid Redirect URIs:" -ForegroundColor Yellow
Write-Host "  - $fullRedirectUri" -ForegroundColor Green
Write-Host "  - $containerAppUrl/*" -ForegroundColor Green

Write-Host "`nWeb Origins:" -ForegroundColor Yellow
Write-Host "  - $containerAppUrl" -ForegroundColor Green

Write-Host "`nValid Post Logout Redirect URIs:" -ForegroundColor Yellow
Write-Host "  - $containerAppUrl/*" -ForegroundColor Green
Write-Host "  - $containerAppUrl/signout-callback-oidc" -ForegroundColor Green

Write-Host "`nTesting Container App Accessibility:" -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri $containerAppUrl -Method Head -TimeoutSec 10
    Write-Host "✓ Container app is accessible (Status: $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "✗ Container app not accessible: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nTesting OIDC Callback Endpoint:" -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri $fullRedirectUri -Method Head -TimeoutSec 10
    Write-Host "✓ OIDC callback endpoint responds (Status: $($response.StatusCode))" -ForegroundColor Green
} catch {
    if ($_.Exception.Response.StatusCode -eq 404) {
        Write-Host "! OIDC callback endpoint returns 404 - this is normal if not authenticated" -ForegroundColor Yellow
    } else {
        Write-Host "✗ OIDC callback endpoint error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`nNext Steps:" -ForegroundColor Cyan
Write-Host "1. Update your Keycloak client with the redirect URIs shown above" -ForegroundColor White
Write-Host "2. Verify your .NET app's OIDC configuration matches your Keycloak settings" -ForegroundColor White
Write-Host "3. Test the authentication flow again" -ForegroundColor White

Write-Host "`nCommon Issues to Check:" -ForegroundColor Cyan
Write-Host "- Ensure Keycloak client has 'Standard Flow' enabled" -ForegroundColor White
Write-Host "- Verify the client secret matches between Keycloak and your app" -ForegroundColor White
Write-Host "- Check that the realm and client ID are correct in your app config" -ForegroundColor White
Write-Host "- Ensure your Keycloak server is accessible from the container app" -ForegroundColor White
