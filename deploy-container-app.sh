# Variables
SOURCE_ACR_NAME=mobileaspectsacr
SOURCE_ACR_RESOURCE_GROUP=Containerlab
IMAGE_NAME=irishome:keycloak
TARGET_SUBSCRIPTION_ID=41ae4571-862f-4682-ac9d-c8e39c9ce301
CONTAINER_APP_RG=mah9-all-cch9-rgr-d01
CONTAINER_APP_ENV=mah9-all-cch9-cae-d01
CONTAINER_APP_NAME=irishome
LOCATION=East US 2

# Log in to Azure
az login

# Set target subscription
az account set --subscription $TARGET_SUBSCRIPTION_ID

# Get ACR credentials (if not using managed identity)
ACR_USERNAME=$(az acr credential show -n $SOURCE_ACR_NAME --query "username" -o tsv)
ACR_PASSWORD=$(az acr credential show -n $SOURCE_ACR_NAME --query "passwords[0].value" -o tsv)
ACR_LOGIN_SERVER=$(az acr show -n $SOURCE_ACR_NAME --query "loginServer" -o tsv)

# Create Container App Environment (if needed)
az containerapp env create \
  --name $CONTAINER_APP_ENV \
  --resource-group $CONTAINER_APP_RG \
  --location $LOCATION

# Deploy Container App
az containerapp create \
  --name $CONTAINER_APP_NAME \
  --resource-group $CONTAINER_APP_RG \
  --environment $CONTAINER_APP_ENV \
  --image $ACR_LOGIN_SERVER/$IMAGE_NAME \
  --registry-server $ACR_LOGIN_SERVER \
  --registry-username $ACR_USERNAME \
  --registry-password $ACR_PASSWORD \
  --ingress external \
  --target-port 80