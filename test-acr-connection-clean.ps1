# Test ACR Connection Script
# This script tests the connection to Azure Container Registry

$acrName = "mobileaspectsacr"
$imageName = "irishome:2.1.0"

Write-Host "Testing ACR connection..." -ForegroundColor Yellow

# Test 1: Check if ACR exists and is accessible
Write-Host "`n1. Checking ACR accessibility..." -ForegroundColor Cyan
$acrResult = az acr show --name $acrName 2>&1
if ($LASTEXITCODE -eq 0) 
{
    $acrInfo = $acrResult | ConvertFrom-Json
    Write-Host "✓ ACR '$acrName' found in resource group: $($acrInfo.resourceGroup)" -ForegroundColor Green
    Write-Host "✓ ACR Login Server: $($acrInfo.loginServer)" -ForegroundColor Green
    Write-Host "✓ Admin User Enabled: $($acrInfo.adminUserEnabled)" -ForegroundColor Green
}
else 
{
    Write-Host "✗ Failed to access ACR '$acrName'" -ForegroundColor Red
    Write-Host "Error: $acrResult" -ForegroundColor Red
    exit 1
}

# Test 2: Enable admin user if not enabled
if (-not $acrInfo.adminUserEnabled) 
{
    Write-Host "`n2. Enabling ACR admin user..." -ForegroundColor Cyan
    az acr update --name $acrName --admin-enabled true
    Write-Host "✓ ACR admin user enabled" -ForegroundColor Green
}
else 
{
    Write-Host "`n2. ACR admin user already enabled" -ForegroundColor Green
}


# Get ACR credentials (if not using managed identity)
Write-Host "Running: az acr credential show -n $acrName --query 'username' -o tsv --subscription $SOURCE_ACR_SUBSCRIPTION_ID"
$rawUsername = az acr credential show -n $acrName --query "username" -o tsv --subscription $SOURCE_ACR_SUBSCRIPTION_ID
Write-Host "Output: $rawUsername"
#$ACR_USERNAME = $rawUsername.Trim()

Write-Host "Running: az acr credential show -n $acrName --query 'passwords[0].value' -o tsv --subscription $SOURCE_ACR_SUBSCRIPTION_ID"
$rawPassword = az acr credential show -n $acrName --query "passwords[0].value" -o tsv --subscription $SOURCE_ACR_SUBSCRIPTION_ID
Write-Host "Output: $rawPassword"
#$ACR_PASSWORD = $rawPassword.Trim()

# Test 3: Get and test credentials
Write-Host "`n3. Testing ACR credentials..." -ForegroundColor Cyan
$credResult = az acr credential show --name $acrName 2>&1
if ($LASTEXITCODE -eq 0) 
{
    $credentials = $credResult | ConvertFrom-Json
    $username = $credentials.username
    $password = $credentials.passwords[0].value
    
    Write-Host "✓ Retrieved ACR credentials" -ForegroundColor Green
    Write-Host "  Username: $username" -ForegroundColor Gray
    Write-Host "  Password: $($password.Substring(0,8))..." -ForegroundColor Gray
}
else 
{
    Write-Host "✗ Failed to retrieve ACR credentials" -ForegroundColor Red
    Write-Host "Error: $credResult" -ForegroundColor Red
    exit 1
}

# Test 4: Test login
Write-Host "`n4. Testing ACR login..." -ForegroundColor Cyan
$loginResult = az acr login --name $acrName 2>&1
if ($LASTEXITCODE -eq 0) 
{
    Write-Host "✓ Successfully logged into ACR" -ForegroundColor Green
}
else 
{
    Write-Host "✗ Failed to login to ACR" -ForegroundColor Red
    Write-Host "Error: $loginResult" -ForegroundColor Red
}

# Test 5: Check if image exists
Write-Host "`n5. Checking if image exists..." -ForegroundColor Cyan
$imageResult = az acr repository show --name $acrName --image $imageName 2>&1
if ($LASTEXITCODE -eq 0) 
{
    $imageInfo = $imageResult | ConvertFrom-Json
    Write-Host "✓ Image '$imageName' found in ACR" -ForegroundColor Green
    Write-Host "  Registry: $($imageInfo.registry)" -ForegroundColor Gray
    Write-Host "  Repository: $($imageInfo.name)" -ForegroundColor Gray
}
else 
{
    Write-Host "✗ Image '$imageName' not found in ACR" -ForegroundColor Red
    Write-Host "Available repositories:" -ForegroundColor Yellow
    az acr repository list --name $acrName --output table
}

# Test 6: Test docker pull (if Docker is available)
Write-Host "`n6. Testing image pull..." -ForegroundColor Cyan
$dockerVersion = docker --version 2>$null
if ($dockerVersion) 
{
    Write-Host "Docker found: $dockerVersion" -ForegroundColor Gray    
}
else 
{
    Write-Host "Docker not available, skipping pull test" -ForegroundColor Yellow
}

$pullResult = docker pull "$($acrInfo.loginServer)/$imageName" 2>&1
if ($LASTEXITCODE -eq 0) 
{
    Write-Host "✓ Successfully pulled image" -ForegroundColor Green
}
else 
{
    Write-Host "✗ Failed to pull image" -ForegroundColor Red
    Write-Host "Error: $pullResult" -ForegroundColor Red
}

Write-Host "`nACR connection test completed!" -ForegroundColor Yellow
Write-Host "`nCredentials for deployment:" -ForegroundColor Cyan
Write-Host "ACR Login Server: $($acrInfo.loginServer)" -ForegroundColor White
Write-Host "Username: $username" -ForegroundColor White
Write-Host "Password: $password" -ForegroundColor White
