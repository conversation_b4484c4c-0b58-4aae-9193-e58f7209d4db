# This PowerShell script deletes the existing Azure Container App and deploys a new one using your ARM template.
# Update the parameter values below as needed.

# Parameters (edit these)
$resourceGroup = "mah9-all-cch9-rgr-d01"
$containerAppName = "irishome"
$containerAppEnvName = "mah9-all-cch9-cae-d01"
$location = "East US 2"
$acrLoginServer = "<your-acr-login-server>"   # e.g. myregistry.azurecr.io
$acrUsername = "<your-acr-username>"
$acrPassword = "<your-acr-password>"
$imageName = "irishome:keycloak"

# Delete the existing container app (ignore error if not found)
Write-Host "Deleting existing container app if it exists..."
az containerapp delete --name $containerAppName --resource-group $resourceGroup --yes

# Deploy the new container app using the ARM template
Write-Host "Deploying new container app using ARM template..."
az deployment group create `
  --resource-group $resourceGroup `
  --template-file "c:\Maspects\Pipelines\container-app-deploy.json" `
  --parameters `
    containerAppEnvName=$containerAppEnvName `
    containerAppName=$containerAppName `
    location="$location" `
    acrLoginServer=$acrLoginServer `
    acrUsername=$acrUsername `
    acrPassword=$acrPassword `
    imageName=$imageName

Write-Host "Deployment complete."
