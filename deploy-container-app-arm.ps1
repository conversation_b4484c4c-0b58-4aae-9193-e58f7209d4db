# This PowerShell script deletes the existing Azure Container App and deploys a new one using your ARM template.
# Update the parameter values below as needed.

# Parameters (edit these)
$resourceGroup = "mah9-all-cch9-rgr-d01"
$containerAppName = "irishome"
$containerAppEnvName = "mah9-all-cch9-cae-d01"
$location = "East US 2"
$acrName = "mobileaspectsacr"  # ACR name without .azurecr.io
$acrLoginServer = "mobileaspectsacr.azurecr.io"   # e.g. myregistry.azurecr.io
$imageName = "irishome:2.1.0"

# Enable ACR admin user and get credentials
Write-Host "Enabling ACR admin user and retrieving credentials..."
az acr update --name $acrName --admin-enabled true

# Get ACR admin credentials
$acrUsername = az acr credential show --name $acrName --query 'username' -o tsv
$acrPassword = az acr credential show --name $acrName --query 'passwords[0].value' -o tsv

Write-Host "Using ACR admin username: $acrUsername"
Write-Host "Password retrieved successfully"

# Delete the existing container app (ignore error if not found)
Write-Host "Deleting existing container app if it exists..."
az containerapp delete --name $containerAppName --resource-group $resourceGroup --yes

# Verify the image exists in ACR
Write-Host "Verifying image exists in ACR..."
$imageExists = az acr repository show --name $acrName --image $imageName 2>$null
if (-not $imageExists) {
    Write-Error "Image $imageName not found in ACR $acrName. Please verify the image name and tag."
    exit 1
}

# Deploy the new container app using the ARM template
Write-Host "Deploying new container app using ARM template..."
az deployment group create `
  --resource-group $resourceGroup `
  --template-file "c:\Maspects\Pipelines\container-app-deploy-simple.json" `
  --parameters `
    containerAppEnvName=$containerAppEnvName `
    containerAppName=$containerAppName `
    containerAppRg=$resourceGroup `
    location="$location" `
    acrLoginServer=$acrLoginServer `
    acrUsername=$acrUsername `
    acrPassword=$acrPassword `
    imageName=$imageName

Write-Host "Deployment complete."
