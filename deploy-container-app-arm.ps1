# This PowerShell script deletes the existing Azure Container App and deploys a new one using your ARM template.
# Update the parameter values below as needed.

# Parameters (edit these)
$resourceGroup = "mah9-all-cch9-rgr-d01"
$containerAppName = "irishome"
$containerAppEnvName = "mah9-all-cch9-cae-d01"
$location = "East US 2"
$acrName = "mobileaspectsacr"  # ACR name without .azurecr.io
$acrLoginServer = "mobileaspectsacr.azurecr.io"   # e.g. myregistry.azurecr.io
$imageName = "irishome:2.1.0"
$acrSubscription = "********-6c21-4a42-a096-945280fd24c0"  # ACR subscription ID
$deploymentSubscription = "41ae4571-862f-4682-ac9d-c8e39c9ce301"  # Deployment subscription ID

# Verify Azure CLI authentication
Write-Host "Verifying Azure CLI authentication..."
$currentAccount = az account show 2>&1
if ($LASTEXITCODE -ne 0) {
    Write-Host "Azure CLI not authenticated. Please run 'az login' first." -ForegroundColor Red
    exit 1
}

# Set subscription for ACR operations
Write-Host "Setting subscription for ACR operations..."
az account set --subscription $acrSubscription
if ($LASTEXITCODE -ne 0) {
    Write-Host "Failed to set ACR subscription. Please verify subscription ID." -ForegroundColor Red
    exit 1
}

# Enable ACR admin user and get credentials
Write-Host "Enabling ACR admin user and retrieving credentials..."
$updateResult = az acr update --name $acrName --admin-enabled true --subscription $acrSubscription 2>&1
if ($LASTEXITCODE -ne 0) {
    Write-Host "Failed to update ACR: $updateResult" -ForegroundColor Red
    exit 1
}

# Get ACR admin credentials with error handling
Write-Host "Retrieving ACR credentials..."
$acrUsername = az acr credential show --name $acrName --query 'username' -o tsv --subscription $acrSubscription 2>&1
if ($LASTEXITCODE -ne 0) {
    Write-Host "Failed to get ACR username: $acrUsername" -ForegroundColor Red
    exit 1
}

$acrPassword = az acr credential show --name $acrName --query 'passwords[0].value' -o tsv --subscription $acrSubscription 2>&1
if ($LASTEXITCODE -ne 0) {
    Write-Host "Failed to get ACR password: $acrPassword" -ForegroundColor Red
    exit 1
}

Write-Host "Using ACR admin username: $acrUsername"
Write-Host "Password retrieved successfully"

# Switch to deployment subscription
Write-Host "Switching to deployment subscription..."
az account set --subscription $deploymentSubscription

# Delete the existing container app (ignore error if not found)
Write-Host "Deleting existing container app if it exists..."
az containerapp delete --name $containerAppName --resource-group $resourceGroup --yes --subscription $deploymentSubscription

# Verify the image exists in ACR (using ACR subscription)
Write-Host "Verifying image exists in ACR..."
$imageExists = az acr repository show --name $acrName --image $imageName --subscription $acrSubscription 2>$null
if (-not $imageExists) {
    Write-Error "Image $imageName not found in ACR $acrName. Please verify the image name and tag."
    Write-Host "Available repositories in ACR:"
    az acr repository list --name $acrName --subscription $acrSubscription --output table
    exit 1
}

# Deploy the new container app using the ARM template (using deployment subscription)
Write-Host "Deploying new container app using ARM template..."
az deployment group create `
  --resource-group $resourceGroup `
  --template-file "c:\Maspects\Pipelines\container-app-deploy-simple.json" `
  --subscription $deploymentSubscription `
  --parameters `
    containerAppEnvName=$containerAppEnvName `
    containerAppName=$containerAppName `
    containerAppRg=$resourceGroup `
    location="$location" `
    acrLoginServer=$acrLoginServer `
    acrUsername=$acrUsername `
    acrPassword=$acrPassword `
    imageName=$imageName

Write-Host "Deployment complete."
