# 'Allow scripts to access the OAuth token' was selected in pipeline.  Add the following YAML to any steps requiring access:
#       env:
#           MY_ACCESS_TOKEN: $(System.AccessToken)
# Variable 'azureRepoPath' was defined in the Variables tab
# Variable 'azureSignToolDirectoryPath' was defined in the Variables tab
# Variable 'branchName' was defined in the Variables tab
# Variable 'ClonePath' was defined in the Variables tab
# Variable 'pathToCloneRepo' was defined in the Variables tab
# Variable 'RepositoryName' was defined in the Variables tab
# Variable Group 'Code Signing' was defined in the Variables tab
name: $(date:yyyyMMdd)$(rev:.r)
jobs:
- job: Job_1
  displayName: Agent job 1
  pool:
    name: MACICD02
    demands:
    - agent.name -equals MACICD02
  steps:
  - checkout: self
    clean: False
    persistCredentials: True
  - task: PowerShell@2
    displayName: Get Branch Name
    inputs:
      targetType: inline
      script: >
        $env:BranchName


        $branchSource = "$(Build.SourceBranch)"


        $branchSourcePath = $branchSource -replace "refs/heads/", ""


        Write-Host "##vso[task.setvariable variable=BranchName;]$branchSourcePath "
  - task: PowerShell@2
    displayName: Git Check_out
    inputs:
      targetType: inline
      script: >
        $Maspects="$(System.DefaultWorkingDirectory)"


        Remove-Item –path $Maspects\* –recurse -force

        cd $Maspects


        git clone $(ClonePath) "$Maspects"

        cd "$Maspects"


        git checkout $(BranchName)

        git status
  - task: NuGetToolInstaller@0
    displayName: Use NuGet
    inputs:
      versionSpec: 6.3.0
  - task: NuGetCommand@2
    displayName: NuGet restore
    inputs:
      solution: Code/iRISMobileApi.sln
      selectOrConfig: config
      nugetConfigPath: C:\\Program Files (x86)\\NuGet\\Config\\Nuget.Config
  - task: VSBuild@1
    displayName: Build solution Code/iRISMobileApi.sln
    inputs:
      solution: Code/iRISMobileApi.sln
      msbuildArgs: /p:DeployOnBuild=true /p:WebPublishMethod=Package /p:PackageAsSingleFile=true /p:SkipInvalidConfigurations=true /p:PackageLocation="$(build.artifactstagingdirectory)"
      platform: $(BuildPlatform)
      configuration: $(BuildConfiguration)
      createLogFile: true
  - task: VSTest@3
    displayName: Run Unit Tests
    continueOnError: True
    inputs:
      testAssemblyVer2: >-
        **\*UnitTest*.dll

        !**\*TestAdapter*.dll

        !**\obj\**
      searchFolder: $(System.DefaultWorkingDirectory)/
      resultsFolder: $(Agent.TempDirectory)\\TestResults
      runOnlyImpactedTests: false
      runAllTestsAfterXBuilds: 10
      publishRunAttachments: false
      rerunFailedTests: false
  - task: PowerShell@2
    displayName: Copy Build files
    inputs:
      targetType: inline
      script: "Expand-Archive -LiteralPath $(build.artifactstagingdirectory)\\iRISMobileApi.zip -DestinationPath $(build.artifactstagingdirectory)\\iRISMobileApiUnzip -Force\n\nif (-not (Test-Path -LiteralPath $(Build.Repository.LocalPath)\\Build\\iRISMobileApi)) {\n    \n        New-Item -Path $(Build.Repository.LocalPath)\\Build\\iRISMobileApi -ItemType Directory -Force\n        Copy-Item '$(build.artifactstagingdirectory)\\iRISMobileApiUnzip\\Content\\D_C\\agent\\_work\\4\\s\\Code\\Api\\iRISMobileApi\\obj\\Debug\\net6.0\\PubTmp\\Out\\*'  -Destination '$(Build.Repository.LocalPath)\\Build\\iRISMobileApi' -Force\n}\nelse {\n    Copy-Item '$(build.artifactstagingdirectory)\\iRISMobileApiUnzip\\Content\\D_C\\agent\\_work\\4\\s\\Code\\Api\\iRISMobileApi\\obj\\Debug\\net6.0\\PubTmp\\Out\\*'  -Destination '$(Build.Repository.LocalPath)\\Build\\iRISMobileApi' -Force\n}\n"
  - task: PowerShell@2
    displayName: Check_in Build Files
    inputs:
      targetType: inline
      script: >
        cd "$(System.DefaultWorkingDirectory)"


        git add Build/*


        git commit -am "Auto  Update Build Files"


        git push origin $(BranchName)
  - task: PowerShell@2
    displayName: Git Code-CheckOut
    inputs:
      filePath: Artifact/Files/Powershell/01 GitCodeCheckout.ps1
      arguments: -AzureRepoPath $(azureRepoPath) -pathToCloneRepo $(pathToCloneRepo) -branchName $(branchName)
  - task: petergroenewegen.PeterGroenewegen-Xpirit-Vsts-Build-InlinePowershell.Xpirit-Vsts-Build-InlinePowershell.InlinePowershell@1
    displayName: Install AzureSignTool
    enabled: False
    inputs:
      Script: "if ((Test-Path $(azureSignToolDirectoryPath)) -eq $false) \n{\n    dotnet tool install AzureSignTool --tool-path $(azureSignToolDirectoryPath) --version 4.0.1\n  }\n  Else\n  {\n  Write-host 'AzureSignTool is already installed'\n  }"
  - task: petergroenewegen.PeterGroenewegen-Xpirit-Vsts-Build-InlinePowershell.Xpirit-Vsts-Build-InlinePowershell.InlinePowershell@1
    displayName: 'Sign The Exe '
    inputs:
      Script: "$FilesList=Get-ChildItem -Path \"$(pathToCloneRepo)\" -File -Recurse | Where-Object  {($_.Name -like \"iRIS*.dll\" -or  $_.Name -like \"iRIS*.exe\" -or $_.Extension -like \"*.exe\" -or  $_.Name -Like \"BC*.dll\" -or $_.Name -Like \"MA*.dll\" -or $_.Name -Like \"RF*.dll\" -or  $_.Name -Like \"PSS*.dll\" -or $_.Name -like \"Debug*\" -or $_.Name -like \"*.ps1\" ) -and $_.Name -notlike \"*.config\" -and $_.Extension -notlike \".pdb\" } | Select-Object -ExpandProperty FullName \n \n Set-Location -Path $(azureSignToolDirectoryPath)\n\nforeach ($file in $FilesList) {\n    $signature = Get-AuthenticodeSignature $file\n    if ($signature.Status -ne \"Valid\" -and $signature.SignerCertificate.Subject -notlike \"*Mobile Aspects, Inc.*\") {\n        .\\azuresigntool.exe sign -kvt \"$(tenantID)\" -kvu \"$(AzureKeyVaultUrl)\" -kvi \"$(ApplicationID)\" -kvs \"$(ClientSecret)\" -kvc \"$(CertificateName)\" -tr \"$(timestampURL)\" -td sha256 -fd sha256 -v \"$file\"\n    }\n    else {\n        Write-Host \"Skipping $file as it is already  signed by Mobile Aspects, Inc.\"\n    }\n}\n"
  - task: PowerShell@2
    displayName: Git Code CheckIn
    inputs:
      filePath: Artifact/Files/Powershell/03 GitCodePush.ps1
      arguments: -PathToClonedRepo $(pathToCloneRepo)
...
