# Debug Container App Configuration
# This script helps debug the container app's OIDC configuration

$resourceGroup = "mah9-all-cch9-rgr-d01"
$containerAppName = "irishome"
$subscription = "41ae4571-862f-4682-ac9d-c8e39c9ce301"

Write-Host "Debugging Container App OIDC Configuration" -ForegroundColor Yellow
Write-Host "============================================" -ForegroundColor Yellow

# Get container app details
Write-Host "`n1. Getting container app details..." -ForegroundColor Cyan
$appDetails = az containerapp show --name $containerAppName --resource-group $resourceGroup --subscription $subscription | ConvertFrom-Json

Write-Host "App Name: $($appDetails.name)" -ForegroundColor White
Write-Host "FQDN: $($appDetails.properties.configuration.ingress.fqdn)" -ForegroundColor White
Write-Host "External Ingress: $($appDetails.properties.configuration.ingress.external)" -ForegroundColor White
Write-Host "Target Port: $($appDetails.properties.configuration.ingress.targetPort)" -ForegroundColor White

# Check environment variables
Write-Host "`n2. Checking environment variables..." -ForegroundColor Cyan
$envVars = $appDetails.properties.template.containers[0].env
if ($envVars) {
    Write-Host "Environment variables found:" -ForegroundColor Green
    foreach ($env in $envVars) {
        if ($env.name -like "*Auth*" -or $env.name -like "*OIDC*" -or $env.name -like "*Keycloak*") {
            Write-Host "  $($env.name): $($env.value)" -ForegroundColor Gray
        }
    }
} else {
    Write-Host "No environment variables found" -ForegroundColor Yellow
}

# Test the actual URLs
$baseUrl = "https://$($appDetails.properties.configuration.ingress.fqdn)"
$signinUrl = "$baseUrl/signin-oidc"

Write-Host "`n3. Testing URLs..." -ForegroundColor Cyan
Write-Host "Base URL: $baseUrl" -ForegroundColor White
Write-Host "Sign-in URL: $signinUrl" -ForegroundColor White

# Test base URL
try {
    $response = Invoke-WebRequest -Uri $baseUrl -Method Head -TimeoutSec 10
    Write-Host "✓ Base URL accessible (Status: $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "✗ Base URL error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test sign-in URL
try {
    $response = Invoke-WebRequest -Uri $signinUrl -Method Head -TimeoutSec 10
    Write-Host "✓ Sign-in URL responds (Status: $($response.StatusCode))" -ForegroundColor Green
} catch {
    if ($_.Exception.Response.StatusCode -eq 404) {
        Write-Host "! Sign-in URL returns 404 - normal if not authenticated" -ForegroundColor Yellow
    } else {
        Write-Host "✗ Sign-in URL error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Get container logs
Write-Host "`n4. Getting recent container logs..." -ForegroundColor Cyan
try {
    $logs = az containerapp logs show --name $containerAppName --resource-group $resourceGroup --subscription $subscription --tail 50
    Write-Host "Recent logs:" -ForegroundColor Green
    Write-Host $logs -ForegroundColor Gray
} catch {
    Write-Host "Could not retrieve logs: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n5. Recommended checks:" -ForegroundColor Cyan
Write-Host "- Verify your appsettings.json has the correct Keycloak Authority URL" -ForegroundColor White
Write-Host "- Check if your app is using HTTPS redirect in production" -ForegroundColor White
Write-Host "- Ensure the container app URL matches exactly in Keycloak" -ForegroundColor White
Write-Host "- Check container app logs for any OIDC-related errors" -ForegroundColor White
