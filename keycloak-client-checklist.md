# Keycloak Client Configuration Checklist for .NET 8.0 App

## Client Settings to Verify

### Basic Settings
- [ ] **Client ID**: Matches what's in your .NET app configuration
- [ ] **Access Type**: Set to `confidential`
- [ ] **Standard Flow Enabled**: `ON`
- [ ] **Client Secret**: Generated and copied to your .NET app

### Redirect URIs
- [ ] **Valid Redirect URIs** includes:
  - `https://irishome.nicetree-50b00068.eastus2.azurecontainerapps.io/signin-oidc`
  - `https://irishome.nicetree-50b00068.eastus2.azurecontainerapps.io/*`

### Web Origins
- [ ] **Web Origins** includes:
  - `https://irishome.nicetree-50b00068.eastus2.azurecontainerapps.io`

### Post Logout Redirect URIs
- [ ] **Valid Post Logout Redirect URIs** includes:
  - `https://irishome.nicetree-50b00068.eastus2.azurecontainerapps.io/*`
  - `https://irishome.nicetree-50b00068.eastus2.azurecontainerapps.io/signout-callback-oidc`

## Optional Settings (depending on your needs)
- [ ] **Direct Access Grants Enabled**: `ON` (if you need username/password login)
- [ ] **Service Accounts Enabled**: `OFF` (unless you need service account)
- [ ] **Authorization Enabled**: `OFF` (unless you need fine-grained permissions)

## Advanced Settings Tab
- [ ] **Access Token Lifespan**: Set appropriately (default is usually fine)
- [ ] **Client Session Idle**: Set appropriately
- [ ] **Client Session Max**: Set appropriately

## Mappers Tab (if needed)
- [ ] Check if you need any custom claim mappers for your .NET app

## How to Navigate in Keycloak Admin Console

1. **Login** → Keycloak Admin Console
2. **Select Realm** → [Your Realm Name]
3. **Clients** → [Your Client ID]
4. **Settings Tab** → Check all the settings above
5. **Save** → After making any changes

## Common Issues and Solutions

### Issue: "Invalid parameter: redirect_uri"
**Solution**: Check Valid Redirect URIs match exactly (including https://)

### Issue: "Client authentication failed"
**Solution**: Verify Access Type is `confidential` and client secret matches

### Issue: "Invalid client"
**Solution**: Check Client ID matches between Keycloak and .NET app

### Issue: "Unauthorized"
**Solution**: Check Standard Flow Enabled is ON

## .NET App Configuration to Match

Your `appsettings.json` should have:
```json
{
  "Authentication": {
    "Schemes": {
      "OpenIdConnect": {
        "Authority": "https://your-keycloak-server/realms/your-realm",
        "ClientId": "[Same as Keycloak Client ID]",
        "ClientSecret": "[Same as Keycloak Client Secret]",
        "ResponseType": "code",
        "CallbackPath": "/signin-oidc",
        "SignedOutCallbackPath": "/signout-callback-oidc"
      }
    }
  }
}
```
