param containerAppEnvName string
param containerAppName string
param location string
param acrLoginServer string
param acrUsername string
@secure()
param acrPassword string
param imageName string
param targetPort int = 80

resource env 'Microsoft.App/managedEnvironments@2023-05-01' = {
  name: containerAppEnvName
  location: location
  properties: {}
}

resource app 'Microsoft.App/containerApps@2023-05-01' = {
  name: containerAppName
  location: location
  dependsOn: [env]
  properties: {
    managedEnvironmentId: env.id
    configuration: {
      ingress: {
        external: true
        targetPort: targetPort
      }
      registries: [
        {
          server: acrLoginServer
          username: acrUsername
          passwordSecretRef: 'acr-pw'
        }
      ]
      secrets: [
        {
          name: 'acr-pw'
          value: acrPassword
        }
      ]
    }
    template: {
      containers: [
        {
          name: containerAppName
          image: '${acrLoginServer}/${imageName}'
        }
      ]
    }
  }
}
